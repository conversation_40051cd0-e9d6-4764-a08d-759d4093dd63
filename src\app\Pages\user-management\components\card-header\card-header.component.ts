import { Component, Input, Output, EventEmitter } from '@angular/core';

export interface CardHeaderAction {
  label: string;
  icon: string;
  action: string;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';
  disabled?: boolean;
}

@Component({
  selector: 'app-card-header',
  standalone: false,
  templateUrl: './card-header.component.html',
  styleUrl: './card-header.component.scss',
})
export class CardHeaderComponent {
  @Input() title: string = '';
  @Input() subtitle?: string;
  @Input() actions: CardHeaderAction[] = [];
  @Input() showBackButton: boolean = false;
  @Input() backButtonLabel: string = 'Back';
  
  @Output() actionClicked = new EventEmitter<string>();
  @Output() backClicked = new EventEmitter<void>();

  onActionClick(action: string): void {
    this.actionClicked.emit(action);
  }

  onBackClick(): void {
    this.backClicked.emit();
  }

  getButtonClass(variant?: string): string {
    switch (variant) {
      case 'primary':
        return 'btn btn-primary';
      case 'secondary':
        return 'btn btn-outline-secondary';
      case 'success':
        return 'btn btn-success';
      case 'warning':
        return 'btn btn-warning';
      case 'danger':
        return 'btn btn-danger';
      case 'info':
        return 'btn btn-info';
      default:
        return 'btn btn-outline-secondary';
    }
  }
}
