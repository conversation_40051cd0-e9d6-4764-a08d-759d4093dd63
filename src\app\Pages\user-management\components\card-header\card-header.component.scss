@use "../../../../shared/scss/user-management-variables.scss" as *;

.card-header-container {
  padding: 1.25rem 1.5rem;
  background: $card-bg;
  border-bottom: 1px solid $card-border;
  border-radius: 0.5rem 0.5rem 0 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  flex-wrap: wrap;
}

.header-text {
  flex: 1;
  min-width: 0; // Allows text to shrink
}

.header-title {
  color: $text-primary;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.header-subtitle {
  color: $text-secondary;
  font-size: 0.95rem;
  margin: 0;
  line-height: 1.4;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

// Button spacing
.header-actions .btn:last-child {
  margin-right: 0 !important;
}

// Responsive design
@media (max-width: 768px) {
  .card-header-container {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .header-actions {
    justify-content: flex-end;
    width: 100%;
  }
  
  .header-title {
    font-size: 1.25rem;
  }
}

@media (max-width: 576px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .header-actions .btn {
    width: 100%;
    margin-right: 0 !important;
  }
}

// Dark theme overrides
:host-context(.dark-theme) {
  .card-header-container {
    background: $dark-card-bg;
    border-bottom-color: $dark-card-border;
  }
  
  .header-title {
    color: $dark-text-primary;
  }
  
  .header-subtitle {
    color: $dark-text-secondary;
  }
}
