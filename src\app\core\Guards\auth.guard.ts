import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { Observable, map, take } from 'rxjs';
import { AuthService } from '../../Pages/authentication/Services/Auth.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.authService.isAuthenticated$.pipe(
      take(1),
      map((isAuthenticated) => {
        // Define auth-related routes
        const authRoutes = [
          '/auth/login',
          '/auth/otp-verification',
          '/auth/forgot-password',
          '/auth/reset-password',
          '/auth/verify-reset-token',
        ];
        const isAuthRoute = authRoutes.some((authRoute) =>
          state.url.startsWith(authRoute),
        );

        if (isAuthenticated && isAuthRoute) {
          // Redirect authenticated users away from auth routes
          this.router.navigate(['/dashboard']);
          return false;
        } else if (!isAuthenticated && !isAuthRoute) {
          // Redirect unauthenticated users to login for non-auth routes
          this.router.navigate(['/auth/login'], {
            queryParams: { returnUrl: state.url },
          });
          return false;
        }
        return true;
      }),
    );
  }
}
