import { Component, OnInit, OnDestroy } from '@angular/core';
import { NotificationService } from '../../../../core/Services';
import { UserManagementService } from '../../services/UserManagement.service';
import { Router } from '@angular/router';
import {
  SortConfig,
  TableColumn,
  UserDetails,
  UserFilterOptions,
  PaginationParameters,
  UserTableRow,
} from '../../Models/UserManagement';
import { CardHeaderAction } from '../../components/card-header/card-header.component';

@Component({
  selector: 'app-user-list',
  standalone: false,
  templateUrl: './user-list.component.html',
  styleUrl: './user-list.component.scss',
})
export class UserListComponent implements OnInit {
  totalItems = 0;
  pageSize = 10;
  pageIndex = 0;
  pageSizeOptions = [5, 10, 25, 50];

  totalRecords = 0;
  users: UserDetails[] = [];
  isLoading = false;

  searchQuery = '';
  selectedRole = 'All';
  selectedStatus = 'All';

  sortConfig: SortConfig = {
    field: 'createdOn',
    direction: 'desc',
  };

  hasAdminAccess = false;

  roleOptions = [
    { label: 'All', value: 'All' },
    { label: 'Admin', value: 'Admin' },
    { label: 'User', value: 'User' },
    { label: 'Super Admin', value: 'Super Admin' },
  ];

  statusOptions = [
    { label: 'All', value: 'All' },
    { label: 'Active', value: 'active' },
    { label: 'Inactive', value: 'inactive' },
  ];

  headerActions: CardHeaderAction[] = [];

  constructor(
    private userManagementService: UserManagementService,
    private notificationService: NotificationService,
    private router: Router,
  ) {
    this.setupHeaderActions();
  }

  ngOnInit(): void {
    this.loadUsers();
  }

  private loadUsers(): void {
    this.isLoading = true;
    const params: PaginationParameters = {
      pageNumber: this.pageIndex + 1,
      pageSize: this.pageSize,
      sortField: this.sortConfig.field,
      sortOrder: this.sortConfig.direction,
      name: this.searchQuery || undefined,
      status: this.selectedStatus === 'All' ? undefined : this.selectedStatus,
      role: this.selectedRole === 'All' ? undefined : this.selectedRole,
    };
    this.userManagementService.getUsers(params).subscribe((pagedResponse) => {
      if (pagedResponse) {
        this.totalItems = pagedResponse.totalItems;
        this.totalRecords = pagedResponse.totalItems;
        this.users = pagedResponse.items;
      }
      this.isLoading = false;
    });
  }

  private resetPaginationAndLoad(): void {
    this.pageIndex = 0;
    this.loadUsers();
  }

  onPageChange(event: any): void {
    this.pageIndex = event.page;
    this.pageSize = event.rows;
    this.loadUsers();
  }

  onSortChange(event: any): void {
    this.sortConfig = {
      field: event.field,
      direction: event.order === 1 ? 'asc' : 'desc',
    };
    this.resetPaginationAndLoad();
  }

  onViewUser(user: UserTableRow): void {
    this.router.navigate(['/user-management/details', user.id]);
  }

  onEditUser(user: UserTableRow): void {
    this.router.navigate(['/user-management/edit', user.id]);
  }

  onStatusChange(user: UserTableRow, newStatus: string): void {
    const isActive = newStatus === 'active';
    this.performStatusToggle(user, isActive);
  }

  private performStatusToggle(user: UserDetails, newStatus: boolean): void {
    if (!user.id) {
      this.notificationService.showError('User ID is missing');
      return;
    }

    const actionText = newStatus ? 'activated' : 'deactivated';

    this.userManagementService
      .deleteUser(user.id, {
        isActive: newStatus,
      })
      .subscribe({
        next: () => {
          this.notificationService.showSuccess(
            `User ${user.fullName || user.email} has been ${actionText} successfully`,
          );
          this.refreshUsers();
        },
      });
  }

  addUser(): void {
    this.router.navigate(['/user-management/add']);
  }

  refreshUsers(): void {
    this.loadUsers();
  }

  viewUser(user: UserTableRow): void {
    this.onViewUser(user);
  }

  editUser(user: UserTableRow): void {
    this.onEditUser(user);
  }

  onSearchChange(): void {
    this.resetPaginationAndLoad();
  }

  onFilterChange(): void {
    this.resetPaginationAndLoad();
  }

  resetFilters(): void {
    this.searchQuery = '';
    this.selectedRole = 'All';
    this.selectedStatus = 'All';
    this.sortConfig = {
      field: 'createdOn',
      direction: 'desc',
    };
    this.resetPaginationAndLoad();
  }

  getProfilePictureUrl(user: UserTableRow): string | undefined {
    if (!user.profileImageUrl) {
      return undefined;
    }

    if (user.profileImageUrl.startsWith('http')) {
      return user.profileImageUrl;
    }

    return user.profileImageUrl;
  }

  getUserInitials(user: UserTableRow): string {
    if (user.fullName) {
      const names = user.fullName.split(' ').filter((name) => name.length > 0);
      if (names.length >= 2) {
        return (names[0][0] + names[1][0]).toUpperCase();
      } else if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase();
      }
    }
    return user.email?.charAt(0).toUpperCase() || 'U';
  }

  private setupHeaderActions(): void {
    this.headerActions = [
      {
        label: 'Add User',
        icon: 'pi-user-plus',
        action: 'add-user',
        variant: 'primary',
      },
    ];
  }

  onHeaderAction(action: string): void {
    switch (action) {
      case 'add-user':
        this.addUser();
        break;
    }
  }
}
