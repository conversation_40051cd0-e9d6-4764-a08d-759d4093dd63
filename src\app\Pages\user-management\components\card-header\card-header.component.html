<div class="card-header-container">
  <div class="header-content">
    <!-- Left side: Title and subtitle -->
    <div class="header-text">
      <h3 class="header-title">{{ title }}</h3>
      <p *ngIf="subtitle" class="header-subtitle">{{ subtitle }}</p>
    </div>
    
    <!-- Right side: Actions -->
    <div class="header-actions">
      <!-- Back button (if enabled) -->
      <button
        *ngIf="showBackButton"
        pButton
        type="button"
        class="btn btn-outline-secondary me-2"
        (click)="onBackClick()"
      >
        <i class="pi pi-arrow-left me-2"></i>{{ backButtonLabel }}
      </button>
      
      <!-- Action buttons -->
      <button
        *ngFor="let action of actions"
        pButton
        type="button"
        [class]="getButtonClass(action.variant)"
        [disabled]="action.disabled"
        (click)="onActionClick(action.action)"
        class="me-2"
      >
        <i [class]="'pi ' + action.icon + ' me-2'"></i>{{ action.label }}
      </button>
    </div>
  </div>
</div>
