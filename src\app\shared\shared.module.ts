import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputSwitchModule } from 'primeng/inputswitch';
import { AuthorizedLayoutComponent } from './Components/authorized-layout/authorized-layout.component';
import { UnauthorizedLayoutComponent } from './Components/unauthorized-layout/unauthorized-layout.component';
import { SideBarComponent } from './Components/side-bar/side-bar.component';
import { AdminAccessDirective } from './directives/admin-access.directive';

@NgModule({
  declarations: [
    AuthorizedLayoutComponent,
    UnauthorizedLayoutComponent,
    SideBarComponent,
    AdminAccessDirective,
  ],
  imports: [CommonModule, FormsModule, ButtonModule, InputSwitchModule],
  exports: [
    AuthorizedLayoutComponent,
    UnauthorizedLayoutComponent,
    SideBarComponent,
    AdminAccessDirective,
  ],
})
export class SharedModule {}
