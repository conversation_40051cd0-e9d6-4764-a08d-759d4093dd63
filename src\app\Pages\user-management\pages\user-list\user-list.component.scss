@use "../../../../shared/scss/user-management-variables.scss" as *;
@use "sass:color";

// User Management Container
.user-management-container {
  padding: 1.5rem;
  background: $user-management-bg;
  min-height: 100vh;
}

// Header Card
.header-card {
  z-index: 10;
  margin-bottom: 1.5rem;
  background: $card-bg;
  border: 1px solid $card-border;
  box-shadow: $shadow-sm;
}

.table-header {
  padding: 1rem;
}

.filter-actions {
  display: grid;
  grid-template-columns: 1fr 200px 200px 200px;
  gap: 1.5rem;
  align-items: end;
}

.filter-label {
  display: block;
  font-weight: 600;
  color: $text-secondary;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section-title {
  color: $text-primary;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;

  i {
    color: $text-secondary;
  }
}

.filter-field {
  flex: 1;
  min-width: 200px;

  &.search-field {
    flex: 2;
  }

  .p-inputtext {
    width: 100%;
    padding: 0.25rem 0.5rem;
    border: 1px solid $border-primary;
    border-radius: $radius-sm;
    background: $bg-secondary;
    color: #000000;

    &:hover {
      border-color: $border-secondary;
    }

    &.p-focus {
      border-color: #ff8c00;
      box-shadow: 0 0 0 0.2rem rgba(#ff8c00, 0.25);
    }
  }

  .p-input-icon-left .pi-search {
    color: #6c757d;
    position: absolute;
  }
  .search-icon {
    position: relative;
    top: 80%;
    transform: translateY(-10%);
    left: 25px;
  }

  .p-dropdown {
    width: 100%;
    border: 1px solid $border-primary;
    border-radius: $radius-sm;
    background: $bg-secondary;

    &:hover {
      border-color: $border-secondary;
    }

    &.p-focus {
      border-color: #ff8c00;
      box-shadow: 0 0 0 0.2rem rgba(#ff8c00, 0.25);
    }
  }
}

.action-field {
  display: flex;
  gap: 1rem;
  margin-left: auto;

  .reset-btn {
    padding: 0.25rem 0.5rem;
    border: 1px solid $border-primary;
    background: $bg-secondary;
    color: #000000;
    border-radius: $radius-sm;

    &:hover {
      background: $bg-hover;
      border-color: $border-secondary;
    }
  }

  .add-user-btn {
    padding: 0.25rem 0.5rem;
    background: #ff8c00;
    color: #ffffff;
    border: none;
    border-radius: $radius-sm;

    &:hover {
      background: color.adjust(#ff8c00, $lightness: -10%);
    }
  }
}

// Loading Container
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
}

.loading-text {
  color: $text-secondary;
  font-size: 1rem;
}

// Table Section
.table-section {
  margin-bottom: 1.5rem;
  th {
    font-size: 15px;
  }
}

.table-card {
  border: none;
  background: $card-bg;
  box-shadow: $shadow-sm;
}

:host ::ng-deep {
  .p-datatable {
    border: none;

    .p-datatable-thead > tr > th {
      background: $bg-secondary;
      color: #000000;
      font-weight: 600;
      font-size: 0.875rem;
      padding: 0.5rem 1rem;
      border-bottom: 2px solid $border-primary;
    }

    .p-datatable-tbody > tr {
      background: #ffffff;

      &:hover {
        background: $bg-hover;
      }

      > td {
        border-bottom: 1px solid $border-secondary;
        padding: 0.5rem 1rem;
        color: #000000;
        font-size: 0.875rem;
      }
    }

    .p-paginator {
      background: $card-bg;
      border-top: 1px solid $border-primary;
      padding: 0.5rem 1rem;
      color: #000000;

      .p-paginator-current {
        color: $text-secondary;
      }

      .p-paginator-prev,
      .p-paginator-next,
      .p-paginator-first,
      .p-paginator-last {
        color: #000000;
        border: 1px solid $border-primary;
        background: $bg-secondary;
        border-radius: $radius-sm;
        width: 32px;
        height: 32px;

        &:hover:not(.p-disabled) {
          background: $bg-hover;
          border-color: $border-secondary;
        }

        &.p-disabled {
          color: $text-muted;
          background: $bg-tertiary;
          border-color: $border-primary;
          opacity: 0.6;
        }
      }
    }
  }
}

// User Info
.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid $border-primary;
  background: $bg-tertiary;
  display: flex;
  align-items: center;
  justify-content: center;

  &.has-image {
    background: transparent;
  }

  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-initials {
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
  }
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
  color: #000000;
}

.user-email {
  color: #6c757d;
  font-size: 0.875rem;
}

// Roles
.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.badge {
  padding: 0.25rem 0.5rem;
  border-radius: $radius-sm;
  font-weight: 500;

  &.badge-primary {
    background: #e3f2fd;
    color: #1565c0;
  }

  &.badge-warning {
    background: #fff3e0;
    color: #ef6c00;
  }

  &.badge-danger {
    background: #ffebee;
    color: #c62828;
  }

  .pi {
    font-size: 0.875rem;
    margin-right: 0.25rem;
  }
}
:host ::ng-deep {
  .p-select-label {
    padding-top: 5.5px;
    font-size: 0.875rem;
  }
  .p-inputtext::placeholder {
    padding-left: 25px;
  }
  .p-inputtext {
    text-align: left !important;
  }
  .p-inputtext,
  .p-select-label,
  .p-dropdown,
  .p-dropdown-label,
  .p-icon {
    color: #818990 !important;
  }
}

// Status
.status-dropdown {
  min-width: 90px;
  max-width: 140px;
  border-radius: $radius-lg;
  background: white;
  color: #000000;

  :host ::ng-deep .p-dropdown-panel {
    .p-dropdown-item {
      padding: 0.25rem 0.5rem;
    }
  }
}

// Created Date
.created-date {
  color: #6c757d;
  font-size: 0.875rem;
}

// Actions
.actions-container {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.action-btn {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  .pi {
    font-size: 0.875rem;
  }
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  color: #6c757d;
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: #000000;
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0 0 0.5rem 0;
}

.empty-state p {
  color: #6c757d;
  font-size: 1rem;
  margin: 0;
}

// Responsive Design
@media (max-width: 767.98px) {
  .user-management-container {
    padding: 1rem;
  }

  .filter-actions {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .filter-field,
  .action-field {
    width: 100%;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .user-avatar {
    width: 32px;
    height: 32px;

    .avatar-initials {
      font-size: 0.875rem;
    }
  }

  .p-datatable-thead > tr > th,
  .p-datatable-tbody > tr > td {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  .actions-container {
    flex-direction: row;
    justify-content: center;
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }
}

// Light Theme Overrides
:host-context(.light-theme) {
  .user-management-container {
    background: $user-management-bg;
  }

  .header-card {
    background: $card-bg;
    border-color: $card-border;
  }

  .filter-field .p-inputtext {
    background: $card-bg;
    border-color: $border-primary;
  }

  .reset-btn {
    background: $card-bg;
    border-color: $card-border;
  }

  .add-user-btn {
    background: #ff8c00;
  }

  .table-card {
    background: $card-bg;
    border-color: $card-border;
  }

  .p-datatable-thead > tr > th {
    background: $bg-secondary;
    border-bottom-color: $border-primary;
  }

  .p-datatable-tbody > tr > td {
    border-bottom-color: $border-secondary;
  }

  .user-name {
    color: #000000;
  }

  .user-email {
    color: #6c757d;
  }

  .created-date {
    color: #6c757d;
  }

  .empty-icon {
    color: #6c757d;
  }

  .empty-state h3 {
    color: #000000;
  }

  .empty-state p {
    color: #6c757d;
  }
}

// Dark Theme Overrides
:host-context(.dark-theme) {
  .user-management-container {
    background: $dark-user-management-bg;
  }

  .header-card,
  .filters-card {
    background: $dark-card-bg;
    border-color: $dark-card-border;
  }

  .section-title {
    color: $dark-text-primary;

    i {
      color: $dark-text-secondary;
    }
  }

  .filter-label {
    color: $dark-text-secondary;
  }

  .filter-field .p-inputtext {
    background: $dark-card-bg;
    border-color: $dark-border-primary;
    color: #ffffff;
  }

  .reset-btn {
    background: $dark-card-bg;
    border-color: $dark-card-border;
    color: #ffffff;
  }

  .add-user-btn {
    background: color.adjust(#ff8c00, $lightness: -10%);
  }

  .table-card {
    background: $dark-card-bg;
    border-color: $dark-card-border;
  }

  .p-datatable-thead > tr > th {
    background: $dark-bg-secondary;
    border-bottom-color: $dark-card-border;
    color: #ffffff;
  }

  .p-datatable-tbody > tr > td {
    border-bottom-color: $dark-border-secondary;
    color: #ffffff;
  }

  .user-name {
    color: #ffffff;
  }

  .user-email {
    color: #b0b0b0;
  }

  .created-date {
    color: #b0b0b0;
  }

  .empty-icon {
    color: #666666;
  }

  .empty-state h3 {
    color: #ffffff;
  }

  .empty-state p {
    color: #b0b0b0;
  }
}
