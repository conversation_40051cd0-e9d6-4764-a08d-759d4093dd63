import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

import { UserManagementRoutingModule } from './user-management-routing.module';
import { UserListComponent } from './pages/user-list/user-list.component';
import { AddEditUserComponent } from './pages/add-edit-user/add-edit-user.component';
import { UserDetailsComponent } from './pages/user-details/user-details.component';
import { CardHeaderComponent } from './components/card-header/card-header.component';
import { AdminAccessDirective } from '../../shared/directives/admin-access.directive';

// PrimeNG Modules
import { TableModule } from 'primeng/table';
import { PaginatorModule } from 'primeng/paginator';
import { InputTextModule } from 'primeng/inputtext';
import { TextareaModule } from 'primeng/textarea';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { InputSwitchModule } from 'primeng/inputswitch';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { SharedModule } from '../../shared/shared.module';

@NgModule({
  declarations: [
    UserListComponent,
    AddEditUserComponent,
    UserDetailsComponent,
    CardHeaderComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    UserManagementRoutingModule,
    // PrimeNG Modules
    TableModule,
    PaginatorModule,
    InputTextModule,
    TextareaModule,
    DropdownModule,
    ButtonModule,
    InputSwitchModule,
    ProgressSpinnerModule,
    ToastModule,
    SharedModule,
  ],
  providers: [MessageService],
})
export class UserManagementModule {}
