import {
  Directive,
  Input,
  TemplateRef,
  ViewContainerRef,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AuthService } from '../../Pages/authentication/Services/Auth.service';

@Directive({
  standalone: false,
  selector: '[appAdminAccess]',
})
export class AdminAccessDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private hasView = false;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private authService: AuthService,
  ) {}

  ngOnInit(): void {
    this.checkAdminAccess();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private checkAdminAccess(): void {
    const userRoles = this.authService.getUserRoles();
    const hasAdminAccess = this.hasAdminRole(userRoles);

    if (hasAdminAccess && !this.hasView) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    } else if (!hasAdminAccess && this.hasView) {
      this.viewContainer.clear();
      this.hasView = false;
    }
  }

  private hasAdminRole(roles: string[]): boolean {
    if (!roles || roles.length === 0) return false;

    const hasOnlyUserRole =
      roles.length === 1 && roles[0].toLowerCase() === 'user';

    if (hasOnlyUserRole) {
      return false;
    }

    const hasAdminRole = roles.some(
      (role) =>
        role.toLowerCase() === 'admin' ||
        role.toLowerCase() === 'super admin' ||
        role.toLowerCase() === 'superadmin',
    );

    return hasAdminRole;
  }
}
