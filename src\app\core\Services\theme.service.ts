import { Injectable, Inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  private currentTheme = 'light';

  constructor(@Inject(DOCUMENT) private document: Document) {
    // Load saved theme from localStorage
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      this.currentTheme = savedTheme;
      this.applyTheme(savedTheme);
    } else {
      // Apply default theme
      this.applyTheme(this.currentTheme);
    }
  }

  toggleTheme(): void {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(this.currentTheme);
    localStorage.setItem('theme', this.currentTheme);
  }

  private applyTheme(theme: string): void {
    // Remove existing theme classes from body
    this.document.body.classList.remove('light-theme', 'dark-theme');

    // Add new theme class to body
    this.document.body.classList.add(`${theme}-theme`);

    // Remove existing theme link
    const existingThemeLink = this.document.getElementById(
      'app-theme',
    ) as HTMLLinkElement;
    if (existingThemeLink) {
      existingThemeLink.remove();
    }

    // Create new theme link
    const themeLink = this.document.createElement('link');
    themeLink.id = 'app-theme';
    themeLink.rel = 'stylesheet';
    themeLink.type = 'text/css';

    // Use CDN or local assets for themes
    if (theme === 'dark') {
      // You can use CDN or local assets
      themeLink.href =
        'https://cdn.jsdelivr.net/npm/primeng@17.18.1/resources/themes/vela-blue/theme.css';
      // Or local: themeLink.href = 'assets/themes/vela-blue/theme.css';
    } else {
      themeLink.href =
        'https://cdn.jsdelivr.net/npm/primeng@17.18.1/resources/themes/saga-blue/theme.css';
      // Or local: themeLink.href = 'assets/themes/saga-blue/theme.css';
    }

    // Add to document head
    this.document.head.appendChild(themeLink);
  }

  getCurrentTheme(): string {
    return this.currentTheme;
  }

  isDarkMode(): boolean {
    return this.currentTheme === 'dark';
  }
}
