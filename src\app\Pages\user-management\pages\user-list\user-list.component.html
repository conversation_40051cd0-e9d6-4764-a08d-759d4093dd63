<div class="user-management-container">
  <!-- Filters Section -->
  <div class="card filters-card mb-4">
    <app-card-header
      title="User Management"
      subtitle="Manage users, roles, and permissions"
      [actions]="headerActions"
      (actionClicked)="onHeaderAction($event)"
    ></app-card-header>
    <!-- <div class="card-header">
      <h5 class="section-title mb-0">
        <i class="pi pi-filter me-2"></i>Filters & Search
      </h5>
    </div> -->
    <div class="card-body">
      <div class="filter-actions">
        <div class="filter-field search-field">
          <label class="filter-label">Search Users</label>
          <span class="p-input-icon-left">
            <i class="pi pi-search search-icon"></i>
            <input
              type="text"
              [style]="{ height: '40px' }"
              pInputText
              placeholder="Search by name or email"
              [(ngModel)]="searchQuery"
              (ngModelChange)="onSearchChange()"
              class="w-100"
            />
          </span>
        </div>
        <div class="filter-field role-field">
          <label class="filter-label">Filter by Role</label>
          <p-dropdown
            [options]="roleOptions"
            [(ngModel)]="selectedRole"
            (ngModelChange)="onFilterChange()"
            placeholder="All Roles"
            [showClear]="true"
            class="w-100"
          ></p-dropdown>
        </div>
        <div class="filter-field status-field">
          <label class="filter-label">Filter by Status</label>
          <p-dropdown
            [options]="statusOptions"
            [(ngModel)]="selectedStatus"
            (ngModelChange)="onFilterChange()"
            placeholder="All Statuses"
            [showClear]="true"
            class="w-100"
          ></p-dropdown>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <p-progressSpinner></p-progressSpinner>
    <p class="loading-text">Loading users...</p>
  </div>

  <!-- User Table -->
  <div class="table-section" *ngIf="!isLoading">
    <div class="card table-card">
      <p-table
        [value]="users"
        [paginator]="true"
        [rows]="pageSize"
        [totalRecords]="totalItems"
        (onPage)="onPageChange($event)"
        [rowsPerPageOptions]="pageSizeOptions"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 20%">User</th>
            <th style="width: 15%">Roles</th>
            <th style="width: 15%">Status</th>
            <th style="width: 15%">Created On</th>
            <th style="width: 15%">Actions</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-user>
          <tr>
            <td>
              <div class="user-info">
                <div
                  class="user-avatar"
                  [ngClass]="{ 'has-image': user.profileImageUrl }"
                >
                  <img
                    *ngIf="user.profileImageUrl"
                    [src]="user.profileImageUrl"
                    class="avatar-image"
                    alt="User Avatar"
                  />
                  <span *ngIf="!user.avatar" class="avatar-initials">{{
                    getUserInitials(user)
                  }}</span>
                </div>
                <div class="user-details">
                  <span class="user-name">{{ user.fullName || "N/A" }}</span>
                  <span class="user-email">{{ user.email || "N/A" }}</span>
                </div>
              </div>
            </td>
            <td>
              <div class="roles-container">
                <span
                  *ngFor="let role of user.roles"
                  class="badge"
                  [ngClass]="{
                    'badge-primary': role === 'User',
                    'badge-warning': role === 'Admin',
                    'badge-danger': role === 'Super Admin',
                  }"
                >
                  {{ role }}
                </span>
              </div>
            </td>
            <td>
              <p-dropdown
                class="status-dropdown"
                [options]="statusOptions"
                [(ngModel)]="user.status"
                optionLabel="label"
                optionValue="value"
                (ngModelChange)="onStatusChange(user, $event)"
              >
                <ng-template let-item pTemplate="selectedItem">
                  <div class="d-flex align-items-center">
                    <i
                      class="pi"
                      [ngClass]="{
                        'pi-check-circle': user.status === 'active',
                        'pi-times-circle': user.status === 'inactive',
                      }"
                    ></i>
                    <span>{{ user.statusDisplay }}</span>
                  </div>
                </ng-template>
                <ng-template pTemplate="item" let-option>
                  <div class="d-flex align-items-center">
                    <i
                      class="pi"
                      [ngClass]="{
                        'pi-check-circle': option.value === 'active',
                        'pi-times-circle': option.value === 'inactive',
                      }"
                    ></i>
                    <span>{{ option.label }}</span>
                  </div>
                </ng-template>
              </p-dropdown>
            </td>
            <td class="created-date">
              {{ user.formattedCreatedOn | date: "dd MMM yyyy" }}
            </td>
            <td>
              <div class="actions-container">
                <button
                  pButton
                  type="button"
                  class="p-button-rounded p-button-info action-btn"
                  (click)="viewUser(user)"
                >
                  <i class="pi pi-eye"></i>
                </button>
                <button
                  pButton
                  type="button"
                  class="p-button-rounded p-button-warning action-btn"
                  (click)="editUser(user)"
                >
                  <i class="pi pi-pencil"></i>
                </button>
              </div>
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <td [attr.colspan]="5" class="empty-state">
            <i class="pi pi-inbox empty-icon"></i>
            <h3>No Users Found</h3>
            <p>Try adjusting your filters or add a new user.</p>
          </td>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>
